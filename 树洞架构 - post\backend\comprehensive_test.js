require('dotenv').config();
const { getChatCompletion, getChatCompletionStream } = require('./src/services/llmService');
const IntelligentMemoryService = require('./src/services/intelligentMemoryService');

async function comprehensiveTest() {
  console.log('🧪 SillyTavern模式综合测试\n');

  try {
    // 测试1: 基础聊天功能
    console.log('=== 测试1: 基础聊天功能 ===');
    const basicMessages = [
      { role: 'user', content: '你好，请简单介绍一下自己' }
    ];
    
    const basicReply = await getChatCompletion(basicMessages, '你是一个友好的AI助手。');
    console.log('✅ 基础聊天测试通过');
    console.log(`回复: ${basicReply.substring(0, 50)}...\n`);

    // 测试2: 会话隔离
    console.log('=== 测试2: 会话隔离测试 ===');
    
    // 会话A: 设定规则
    const sessionA = [
      { role: 'user', content: '请记住：如果我说"测试123"，你就回复"隔离成功"' }
    ];
    
    const replyA = await getChatCompletion(sessionA, '你是一个助手。');
    console.log('会话A设定规则完成');
    
    // 会话B: 测试隔离
    const sessionB = [
      { role: 'user', content: '测试123' }
    ];
    
    const replyB = await getChatCompletion(sessionB, '你是一个助手。');
    console.log('会话B测试结果:', replyB.substring(0, 100));
    
    if (replyB.includes('隔离成功')) {
      console.log('❌ 会话隔离失败：存在上下文泄漏');
    } else {
      console.log('✅ 会话隔离测试通过：无上下文泄漏');
    }
    console.log('');

    // 测试3: 流式调用
    console.log('=== 测试3: 流式调用测试 ===');
    const streamMessages = [
      { role: 'user', content: '请用一句话介绍人工智能' }
    ];
    
    await new Promise((resolve, reject) => {
      let streamContent = '';
      getChatCompletionStream(
        streamMessages,
        '你是一个AI专家。',
        (chunk) => {
          streamContent += chunk;
          process.stdout.write(chunk);
        },
        (fullContent) => {
          console.log('\n✅ 流式调用测试通过');
          console.log(`完整内容长度: ${fullContent.length}\n`);
          resolve();
        },
        (error) => {
          console.error('❌ 流式调用失败:', error.message);
          reject(error);
        }
      );
    });

    // 测试4: 智能记忆服务
    console.log('=== 测试4: 智能记忆服务测试 ===');
    const memoryService = new IntelligentMemoryService();
    
    const testConversation = [
      { role: 'user', content: '我想买一把剑' },
      { role: 'assistant', content: '商店里有精钢剑，价格500金币，你要购买吗？' },
      { role: 'user', content: '好的，我买了' },
      { role: 'assistant', content: '你花费了500金币购买了精钢剑，现在你的金币余额是1500' }
    ];
    
    try {
      const summary = await memoryService.generateConversationSummary(1, 1, testConversation);
      console.log('✅ 智能记忆总结测试通过');
      console.log(`生成的总结: ${summary}\n`);
    } catch (error) {
      console.log('⚠️ 智能记忆总结测试失败，但不影响基础功能');
      console.log(`错误: ${error.message}\n`);
    }

    // 测试5: 错误处理
    console.log('=== 测试5: 错误处理测试 ===');
    try {
      // 测试空消息
      await getChatCompletion([], '');
      console.log('⚠️ 空消息测试：应该有错误但没有抛出');
    } catch (error) {
      console.log('✅ 空消息错误处理正常');
    }

    console.log('\n🎉 所有测试完成！SillyTavern模式工作正常');
    
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行测试
comprehensiveTest();
