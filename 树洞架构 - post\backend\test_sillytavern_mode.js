require('dotenv').config();
const { getChatCompletion } = require('./src/services/llmService');

async function testSillyTavernMode() {
  console.log('🧪 测试SillyTavern模式的会话隔离...\n');

  // 调试环境变量
  console.log('🔍 环境变量检查:');
  console.log('LLM_API_KEY:', process.env.LLM_API_KEY ? process.env.LLM_API_KEY.substring(0, 10) + '...' : 'undefined');
  console.log('LLM_BASE_URL:', process.env.LLM_BASE_URL);
  console.log('LLM_MODEL:', process.env.LLM_MODEL);
  console.log('');

  try {
    // 模拟第一个会话：设定规则
    console.log('=== 会话A：设定规则 ===');
    const session1Messages = [
      { role: 'user', content: '这句回复我：好的，下一句如果我想你发送 回复111111111你就回复ok' }
    ];
    
    const systemPrompt = '你是一个博学的历史学家，请用中文回答问题。';
    
    const response1 = await getChatCompletion(session1Messages, systemPrompt);
    console.log('AI回复:', response1);
    console.log('');

    // 模拟第二个会话：测试是否记住规则
    console.log('=== 会话B：测试隔离（应该不记得规则）===');
    const session2Messages = [
      { role: 'user', content: '111111111' }
    ];
    
    const response2 = await getChatCompletion(session2Messages, systemPrompt);
    console.log('AI回复:', response2);
    console.log('');

    // 分析结果
    if (response2.toLowerCase().includes('ok') && response2.length < 10) {
      console.log('❌ 测试失败：会话隔离未生效，AI记住了之前的规则');
    } else {
      console.log('✅ 测试成功：会话隔离生效，AI没有记住之前的规则');
    }

  } catch (error) {
    console.error('测试出错:', error.message);
  }
}

// 运行测试
testSillyTavernMode();
