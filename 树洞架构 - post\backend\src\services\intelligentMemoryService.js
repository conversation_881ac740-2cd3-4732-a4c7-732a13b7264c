const { query } = require('../config/database');
const VectorMemoryService = require('./vectorMemoryService');

/**
 * 智能记忆服务 - 基于SillyTavern设计理念
 * 功能：
 * 1. 状态跟踪和提取
 * 2. 智能记忆总结
 * 3. 上下文管理
 * 4. 人物属性变化追踪
 */
class IntelligentMemoryService {
  constructor() {
    this.vectorMemoryService = require('./vectorMemoryService');
  }

  /**
   * 从对话中提取和更新人物状态
   */
  async extractAndUpdateCharacterState(userId, cardId, conversations, conversationId = null) {
    console.log('🧠 开始提取人物状态...');
    
    try {
      // 1. 分析对话内容，提取状态变化
      const stateChanges = await this.analyzeConversationForStateChanges(conversations);
      
      // 2. 获取当前状态
      const currentState = await this.getCurrentCharacterState(userId, cardId);
      
      // 3. 应用状态变化
      const newState = await this.applyStateChanges(currentState, stateChanges);
      
      // 4. 保存新状态
      await this.saveCharacterState(userId, cardId, newState, stateChanges);
      
      console.log('✅ 人物状态更新完成');
      return { currentState, newState, stateChanges };
      
    } catch (error) {
      console.error('❌ 提取人物状态失败:', error);
      throw error;
    }
  }

  /**
   * 分析对话内容，提取状态变化
   */
  async analyzeConversationForStateChanges(conversations) {
    const conversationText = conversations.map(c => 
      `${c.role === 'user' ? '用户' : 'AI'}: ${c.content}`
    ).join('\n');

    // 使用LLM分析对话，提取状态变化
    const analysisPrompt = `
分析以下对话，提取出用户的状态变化信息。
只返回发生的具体变化，使用JSON格式：

对话内容：
${conversationText}

请提取以下类型的状态变化：
1. 健康值变化（health）：如受伤、治疗等
2. 金钱变化（money）：如获得、花费金钱
3. 经验变化（experience）：如完成任务、学习新技能
4. 物品变化（inventory）：如获得、失去物品
5. 关系变化（relationships）：如与某人关系改善/恶化
6. 位置变化（location）：如移动到新地点
7. 情绪变化（mood）：如高兴、悲伤、愤怒等
8. 技能变化（skills）：如学会新技能、技能提升
9. 任务状态（quests）：如接受、完成、失败任务
10. 属性变化（attributes）：如力量、智力等属性变化

返回格式：
{
  "health": { "change": +10, "reason": "喝了治疗药水" },
  "money": { "change": -50, "reason": "购买了装备" },
  "inventory": { 
    "add": [{"item": "铁剑", "quantity": 1}],
    "remove": [{"item": "木剑", "quantity": 1}]
  },
  "experience": { "change": +100, "reason": "完成了任务" },
  "mood": { "value": "开心", "reason": "成功完成挑战" },
  "location": { "value": "森林", "previous": "村庄", "reason": "决定去探险" },
  "relationships": {
    "NPC_艾莉丝": { "change": +10, "reason": "帮助了她" }
  },
  "skills": {
    "剑术": { "change": +5, "reason": "通过战斗练习" }
  },
  "quests": {
    "拯救公主": { "status": "完成", "reason": "成功营救了公主" }
  },
  "attributes": {
    "力量": { "change": +2, "reason": "完成了力量训练" }
  }
}

如果没有状态变化，返回空对象 {}
`;

    try {
      // 这里应该调用LLM API进行分析
      // 暂时返回模拟数据进行测试
      const mockStateChanges = this.extractStateChangesFromText(conversationText);
      return mockStateChanges;
      
    } catch (error) {
      console.error('分析对话状态变化失败:', error);
      return {};
    }
  }

  /**
   * 简单的状态变化提取（基于关键词，后续可替换为LLM）
   */
  extractStateChangesFromText(text) {
    const changes = {};
    
    // 健康值变化
    if (text.includes('受伤') || text.includes('伤害')) {
      changes.health = { change: -10, reason: '受到伤害' };
    }
    if (text.includes('治疗') || text.includes('恢复')) {
      changes.health = { change: +15, reason: '得到治疗' };
    }
    
    // 金钱变化
    const moneyRegex = /(?:获得|得到|赚取).*?(\d+).*?(?:金币|金钱|元)/;
    const moneyMatch = text.match(moneyRegex);
    if (moneyMatch) {
      changes.money = { change: +parseInt(moneyMatch[1]), reason: '获得金钱' };
    }
    
    const spendRegex = /(?:花费|消费|支付).*?(\d+).*?(?:金币|金钱|元)/;
    const spendMatch = text.match(spendRegex);
    if (spendMatch) {
      changes.money = { change: -parseInt(spendMatch[1]), reason: '花费金钱' };
    }
    
    // 经验变化
    if (text.includes('完成任务') || text.includes('成功') || text.includes('胜利')) {
      changes.experience = { change: +50, reason: '完成挑战' };
    }
    
    // 物品变化
    const itemRegex = /(?:获得|得到).*?([\u4e00-\u9fff]+(?:剑|盾|药水|装备))/;
    const itemMatch = text.match(itemRegex);
    if (itemMatch) {
      changes.inventory = {
        add: [{ item: itemMatch[1], quantity: 1 }]
      };
    }
    
    // 情绪变化
    if (text.includes('高兴') || text.includes('开心') || text.includes('兴奋')) {
      changes.mood = { value: '开心', reason: '心情愉悦' };
    }
    if (text.includes('悲伤') || text.includes('难过') || text.includes('沮丧')) {
      changes.mood = { value: '悲伤', reason: '心情低落' };
    }
    
    // 位置变化
    const locationRegex = /(?:来到|前往|到达).*?([\u4e00-\u9fff]{2,6})/;
    const locationMatch = text.match(locationRegex);
    if (locationMatch) {
      changes.location = { value: locationMatch[1], reason: '移动到新地点' };
    }
    
    return changes;
  }

  /**
   * 获取当前人物状态
   */
  async getCurrentCharacterState(userId, cardId) {
    try {
      const result = await query(`
        SELECT * FROM user_status 
        WHERE user_id = ? AND card_id = ? 
        ORDER BY updated_at DESC 
        LIMIT 1
      `, [userId, cardId]);

      if (result.length > 0) {
        const state = result[0];
        console.log('📊 数据库状态数据:', state);
        
        // 安全解析JSON字段
        let inventory = [];
        if (state.inventory) {
          if (Array.isArray(state.inventory)) {
            inventory = state.inventory;
          } else if (typeof state.inventory === 'string') {
            try {
              inventory = JSON.parse(state.inventory);
            } catch (e) {
              console.log('⚠️ inventory解析失败，使用默认值');
              inventory = [];
            }
          }
        }

        let relationships = {};
        if (state.relationships) {
          if (typeof state.relationships === 'object' && !Array.isArray(state.relationships)) {
            relationships = state.relationships;
          } else if (typeof state.relationships === 'string') {
            try {
              relationships = JSON.parse(state.relationships);
            } catch (e) {
              console.log('⚠️ relationships解析失败，使用默认值');
              relationships = {};
            }
          }
        }

        let skills = {};
        if (state.skills) {
          if (typeof state.skills === 'object' && !Array.isArray(state.skills)) {
            skills = state.skills;
          } else if (typeof state.skills === 'string') {
            try {
              skills = JSON.parse(state.skills);
            } catch (e) {
              console.log('⚠️ skills解析失败，使用默认值');
              skills = {};
            }
          }
        }

        let quests = {};
        if (state.quest_status) {
          if (typeof state.quest_status === 'object' && !Array.isArray(state.quest_status)) {
            quests = state.quest_status;
          } else if (typeof state.quest_status === 'string') {
            try {
              quests = JSON.parse(state.quest_status);
            } catch (e) {
              console.log('⚠️ quest_status解析失败，使用默认值');
              quests = {};
            }
          }
        }

        let attributes = {
          strength: 10,
          intelligence: 10,
          dexterity: 10,
          constitution: 10,
          wisdom: 10,
          charisma: 10
        };
        if (state.attributes) {
          if (typeof state.attributes === 'object' && !Array.isArray(state.attributes)) {
            attributes = { ...attributes, ...state.attributes };
          } else if (typeof state.attributes === 'string') {
            try {
              const parsedAttributes = JSON.parse(state.attributes);
              attributes = { ...attributes, ...parsedAttributes };
            } catch (e) {
              console.log('⚠️ attributes解析失败，使用默认值');
            }
          }
        }
        
        return {
          health: state.health || 100,
          mana: state.mana || 100,
          money: state.gold || 0,  // 注意：数据库字段是gold，返回为money
          experience: state.experience || 0,
          level: state.level || 1,
          inventory: inventory,
          mood: state.mood || 'neutral',
          location: state.location || '起始地点',
          relationships: relationships,
          skills: skills,
          quests: quests,
          attributes: attributes
        };
      }

      // 返回默认状态
      return {
        health: 100,
        mana: 100,
        money: 0,
        experience: 0,
        level: 1,
        inventory: [],
        mood: 'neutral',
        location: '起始地点',
        relationships: {},
        skills: {},
        quests: {},
        attributes: {
          strength: 10,
          intelligence: 10,
          dexterity: 10,
          constitution: 10,
          wisdom: 10,
          charisma: 10
        }
      };
    } catch (error) {
      console.error('获取人物状态失败:', error);
      throw error;
    }
  }

  /**
   * 应用状态变化
   */
  async applyStateChanges(currentState, stateChanges) {
    const newState = { ...currentState };

    // 应用健康值变化
    if (stateChanges.health) {
      newState.health = Math.max(0, Math.min(100, newState.health + stateChanges.health.change));
    }

    // 应用金钱变化
    if (stateChanges.money) {
      newState.money = Math.max(0, newState.money + stateChanges.money.change);
    }

    // 应用经验变化和等级提升
    if (stateChanges.experience) {
      newState.experience += stateChanges.experience.change;
      
      // 检查是否升级（每100经验一级）
      const newLevel = Math.floor(newState.experience / 100) + 1;
      if (newLevel > newState.level) {
        newState.level = newLevel;
        // 升级时恢复血量和法力
        newState.health = Math.min(100, newState.health + 20);
        newState.mana = Math.min(100, newState.mana + 20);
      }
    }

    // 应用物品变化
    if (stateChanges.inventory) {
      if (stateChanges.inventory.add) {
        for (const item of stateChanges.inventory.add) {
          const existingItem = newState.inventory.find(i => i.item === item.item);
          if (existingItem) {
            existingItem.quantity += item.quantity;
          } else {
            newState.inventory.push(item);
          }
        }
      }
      
      if (stateChanges.inventory.remove) {
        for (const item of stateChanges.inventory.remove) {
          const existingItem = newState.inventory.find(i => i.item === item.item);
          if (existingItem) {
            existingItem.quantity -= item.quantity;
            if (existingItem.quantity <= 0) {
              newState.inventory = newState.inventory.filter(i => i.item !== item.item);
            }
          }
        }
      }
    }

    // 应用情绪变化
    if (stateChanges.mood) {
      newState.mood = stateChanges.mood.value;
    }

    // 应用位置变化
    if (stateChanges.location) {
      newState.location = stateChanges.location.value;
    }

    // 应用关系变化
    if (stateChanges.relationships) {
      for (const [npc, change] of Object.entries(stateChanges.relationships)) {
        newState.relationships[npc] = (newState.relationships[npc] || 0) + change.change;
      }
    }

    // 应用技能变化
    if (stateChanges.skills) {
      for (const [skill, change] of Object.entries(stateChanges.skills)) {
        newState.skills[skill] = (newState.skills[skill] || 0) + change.change;
      }
    }

    // 应用任务状态变化
    if (stateChanges.quests) {
      Object.assign(newState.quests, stateChanges.quests);
    }

    // 应用属性变化
    if (stateChanges.attributes) {
      for (const [attr, change] of Object.entries(stateChanges.attributes)) {
        newState.attributes[attr] = (newState.attributes[attr] || 10) + change.change;
      }
    }

    return newState;
  }

  /**
   * 保存人物状态
   */
  async saveCharacterState(userId, cardId, newState, stateChanges) {
    try {
      // 更新user_status表
      await query(`
        INSERT INTO user_status 
        (user_id, card_id, health, mana, gold, level, experience, inventory, 
         mood, location, relationships, skills, quest_status, attributes, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ON DUPLICATE KEY UPDATE
        health = VALUES(health),
        mana = VALUES(mana),
        gold = VALUES(gold),
        level = VALUES(level),
        experience = VALUES(experience),
        inventory = VALUES(inventory),
        mood = VALUES(mood),
        location = VALUES(location),
        relationships = VALUES(relationships),
        skills = VALUES(skills),
        quest_status = VALUES(quest_status),
        attributes = VALUES(attributes),
        updated_at = NOW()
      `, [
        userId, cardId, newState.health, newState.mana, newState.money,
        newState.level, newState.experience, JSON.stringify(newState.inventory),
        newState.mood, newState.location, JSON.stringify(newState.relationships),
        JSON.stringify(newState.skills), JSON.stringify(newState.quests),
        JSON.stringify(newState.attributes)
      ]);

      // 如果有状态变化，创建状态变化记忆
      if (Object.keys(stateChanges).length > 0) {
        await this.createStateChangeMemory(userId, cardId, stateChanges, newState, conversationId);
      }

    } catch (error) {
      console.error('保存人物状态失败:', error);
      throw error;
    }
  }

  /**
   * 创建状态变化记忆
   */
  async createStateChangeMemory(userId, cardId, stateChanges, newState, conversationId = null) {
    const stateChangeSummary = this.generateStateChangeSummary(stateChanges, newState);
    
    await this.vectorMemoryService.createUserMemory({
      userId,
      cardId,
      conversationId,
      content: stateChangeSummary,
      memoryType: 'status_change',
      importance: 0.7, // 状态变化比较重要
      context: {
        stateChanges,
        newState: {
          health: newState.health,
          money: newState.money,
          level: newState.level,
          experience: newState.experience,
          mood: newState.mood,
          location: newState.location
        }
      }
    });
  }

  /**
   * 生成状态变化总结
   */
  generateStateChangeSummary(stateChanges, newState) {
    const summaryParts = [];

    if (stateChanges.health) {
      const change = stateChanges.health.change;
      summaryParts.push(`健康值${change > 0 ? '增加' : '减少'}${Math.abs(change)}点 (${stateChanges.health.reason})`);
    }

    if (stateChanges.money) {
      const change = stateChanges.money.change;
      summaryParts.push(`金钱${change > 0 ? '增加' : '减少'}${Math.abs(change)}金币 (${stateChanges.money.reason})`);
    }

    if (stateChanges.experience) {
      summaryParts.push(`获得${stateChanges.experience.change}点经验 (${stateChanges.experience.reason})`);
    }

    if (stateChanges.inventory) {
      if (stateChanges.inventory.add) {
        const items = stateChanges.inventory.add.map(item => `${item.item}×${item.quantity}`).join('、');
        summaryParts.push(`获得物品：${items}`);
      }
      if (stateChanges.inventory.remove) {
        const items = stateChanges.inventory.remove.map(item => `${item.item}×${item.quantity}`).join('、');
        summaryParts.push(`失去物品：${items}`);
      }
    }

    if (stateChanges.mood) {
      summaryParts.push(`情绪变为${stateChanges.mood.value} (${stateChanges.mood.reason})`);
    }

    if (stateChanges.location) {
      summaryParts.push(`移动到${stateChanges.location.value} (${stateChanges.location.reason})`);
    }

    if (stateChanges.relationships) {
      for (const [npc, change] of Object.entries(stateChanges.relationships)) {
        summaryParts.push(`与${npc}的关系${change.change > 0 ? '改善' : '恶化'} (${change.reason})`);
      }
    }

    if (stateChanges.skills) {
      for (const [skill, change] of Object.entries(stateChanges.skills)) {
        summaryParts.push(`${skill}技能提升${change.change}点 (${change.reason})`);
      }
    }

    if (stateChanges.quests) {
      for (const [quest, info] of Object.entries(stateChanges.quests)) {
        summaryParts.push(`任务"${quest}"${info.status} (${info.reason})`);
      }
    }

    if (stateChanges.attributes) {
      for (const [attr, change] of Object.entries(stateChanges.attributes)) {
        summaryParts.push(`${attr}属性增加${change.change}点 (${change.reason})`);
      }
    }

    const summary = summaryParts.join('；') || '无状态变化';
    return `状态更新：${summary}。当前状态 - 健康值:${newState.health} 金钱:${newState.money} 等级:${newState.level} 位置:${newState.location}`;
  }

  /**
   * 生成智能记忆总结
   */
  async generateConversationSummary(userId, cardId, conversations) {
    console.log('📝 生成对话总结...');
    
    try {
      const conversationText = conversations.map(c => 
        `${c.role === 'user' ? '用户' : 'AI'}: ${c.content}`
      ).join('\n');

      // 调用LLM生成智能总结
      const summary = await this.generateLLMSummary(conversationText);
      return summary;
    } catch (error) {
      console.error('LLM总结生成失败，使用简单总结:', error.message);
      // 降级到简单总结
      const conversationText = conversations.map(c => 
        `${c.role === 'user' ? '用户' : 'AI'}: ${c.content}`
      ).join('\n');
      return this.generateSimpleSummary(conversationText);
    }
  }

  /**
   * 使用LLM生成智能总结 - SillyTavern模式
   */
  async generateLLMSummary(conversationText) {
    const llmService = require('./llmService');

    const systemPrompt = `你是一个专业的对话总结助手。请为用户提供的对话生成简洁但有意义的记忆总结。

总结要求：
1. 提取对话的核心内容和关键信息
2. 包含重要的事件、行动或决定
3. 保持客观且简洁(30字以内)
4. 避免"进行了一般性对话"这种无意义描述

请直接输出总结，不要包含其他解释文字。`;

    const userMessage = {
      role: 'user',
      content: `请为以下对话生成总结：\n\n${conversationText}`
    };

    try {
      // SillyTavern模式：使用消息数组而不是直接的prompt
      const summary = await llmService.getChatCompletion([userMessage], systemPrompt);

      if (summary && summary.length > 5 && !summary.includes('一般性对话')) {
        console.log('✅ LLM总结生成成功:', summary);
        return summary;
      } else {
        console.log('⚠️ LLM返回无效总结:', summary);
        throw new Error('LLM返回无效总结');
      }
    } catch (error) {
      console.error('❌ LLM API调用失败:', error.message);
      throw error;
    }
  }

  /**
   * 简单总结生成（后续可替换为LLM）
   */
  generateSimpleSummary(text) {
    // 提取关键信息
    const keywords = this.extractKeywords(text);
    const actions = this.extractActions(text);
    const emotions = this.extractEmotions(text);
    
    const summaryParts = [];
    
    if (keywords.length > 0) {
      summaryParts.push(`讨论了${keywords.slice(0, 3).join('、')}`);
    }
    
    if (actions.length > 0) {
      summaryParts.push(`进行了${actions.slice(0, 2).join('、')}`);
    }
    
    if (emotions.length > 0) {
      summaryParts.push(`情绪表现为${emotions[0]}`);
    }
    
    return summaryParts.join('，') || '进行了一般性对话';
  }

  /**
   * 提取关键词
   */
  extractKeywords(text) {
    const keywords = [];
    const keywordPatterns = [
      /任务|quest|mission/gi,
      /战斗|battle|fight/gi,
      /探险|adventure|explore/gi,
      /商店|shop|store/gi,
      /朋友|friend/gi,
      /敌人|enemy/gi,
      /宝藏|treasure/gi,
      /技能|skill/gi,
      /魔法|magic/gi,
      /装备|equipment/gi
    ];
    
    for (const pattern of keywordPatterns) {
      if (pattern.test(text)) {
        keywords.push(pattern.source.split('|')[0]);
      }
    }
    
    return keywords;
  }

  /**
   * 提取动作
   */
  extractActions(text) {
    const actions = [];
    const actionPatterns = [
      /购买|买/gi,
      /战斗|打击/gi,
      /探索|寻找/gi,
      /学习|练习/gi,
      /帮助|协助/gi,
      /移动|前往/gi
    ];
    
    for (const pattern of actionPatterns) {
      if (pattern.test(text)) {
        actions.push(pattern.source.split('|')[0]);
      }
    }
    
    return actions;
  }

  /**
   * 提取情绪
   */
  extractEmotions(text) {
    const emotions = [];
    const emotionPatterns = [
      { pattern: /高兴|开心|兴奋|快乐/gi, emotion: '开心' },
      { pattern: /悲伤|难过|沮丧|失望/gi, emotion: '悲伤' },
      { pattern: /愤怒|生气|愤怒/gi, emotion: '愤怒' },
      { pattern: /害怕|恐惧|紧张/gi, emotion: '害怕' },
      { pattern: /惊讶|震惊|意外/gi, emotion: '惊讶' }
    ];
    
    for (const { pattern, emotion } of emotionPatterns) {
      if (pattern.test(text)) {
        emotions.push(emotion);
      }
    }
    
    return emotions;
  }

  /**
   * 处理对话并创建智能记忆
   */
  async processConversationIntelligently(userId, cardId, conversations, context = {}) {
    console.log('🧠 智能处理对话开始...');
    
    // 从context中提取conversationId
    const conversationId = context.sessionId || context.conversationId || null;
    console.log(`📋 对话ID: ${conversationId}`);
    
    try {
      // 1. 提取和更新人物状态
      const stateResult = await this.extractAndUpdateCharacterState(userId, cardId, conversations, conversationId);
      
      // 2. 生成对话总结
      const summary = await this.generateConversationSummary(userId, cardId, conversations);
      
      // 3. 创建总结记忆
      await this.vectorMemoryService.createUserMemory({
        userId,
        cardId,
        conversationId,
        content: summary,
        memoryType: 'conversation',
        importance: 0.6,
        context: {
          originalLength: conversations.length,
          stateChanges: stateResult.stateChanges,
          timestamp: new Date().toISOString()
        }
      });
      
      console.log('✅ 智能对话处理完成');
      
      return {
        summary,
        stateChanges: stateResult.stateChanges,
        newState: stateResult.newState,
        currentState: stateResult.currentState
      };
      
    } catch (error) {
      console.error('❌ 智能对话处理失败:', error);
      throw error;
    }
  }
}

module.exports = new IntelligentMemoryService();