const { query } = require('./src/config/database');
const { getChatCompletionStream } = require('./src/services/llmService');

async function testLLMIsolation() {
  try {
    console.log('=== 测试LLM上下文隔离 ===\n');
    
    // 模拟两个完全不同的会话
    const session1Messages = [
      { role: 'user', content: '请记住：从现在开始只用中文回复，并且在每句话前加上[中文]' }
    ];
    
    const session2Messages = [
      { role: 'user', content: 'Please remember: from now on, only reply in English and add [ENGLISH] before each sentence' }
    ];
    
    console.log('🔄 测试1：发送中文指令...');
    await new Promise((resolve, reject) => {
      getChatCompletionStream(
        session1Messages,
        '你是一个助手。', // SillyTavern模式：系统提示词作为独立参数
        (chunk) => {
          process.stdout.write(chunk);
        },
        (fullResponse) => {
          console.log(`\n✅ 会话1完成: ${fullResponse.substring(0, 100)}...\n`);
          resolve();
        },
        (error) => {
          console.error('❌ 会话1错误:', error);
          reject(error);
        }
      );
    });
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('🔄 测试2：发送英文指令（模拟新会话）...');
    await new Promise((resolve, reject) => {
      getChatCompletionStream(
        session2Messages,
        'You are an assistant.', // SillyTavern模式：系统提示词作为独立参数
        (chunk) => {
          process.stdout.write(chunk);
        },
        (fullResponse) => {
          console.log(`\n✅ 会话2完成: ${fullResponse.substring(0, 100)}...\n`);
          resolve();
        },
        (error) => {
          console.error('❌ 会话2错误:', error);
          reject(error);
        }
      );
    });
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log('🔄 测试3：第二个会话发送简单测试...');
    const session2Test = [
      { role: 'user', content: 'Please remember: from now on, only reply in English and add [ENGLISH] before each sentence' },
      { role: 'assistant', content: '[ENGLISH] I understand. From now on, I will only reply in English and add [ENGLISH] before each sentence.' },
      { role: 'user', content: 'What is 1+1?' }
    ];
    
    await new Promise((resolve, reject) => {
      getChatCompletionStream(
        session2Test,
        'You are an assistant.', // SillyTavern模式：系统提示词作为独立参数
        (chunk) => {
          process.stdout.write(chunk);
        },
        (fullResponse) => {
          console.log(`\n✅ 会话2测试完成: ${fullResponse.substring(0, 100)}...\n`);
          resolve();
        },
        (error) => {
          console.error('❌ 会话2测试错误:', error);
          reject(error);
        }
      );
    });
    
    process.exit(0);
  } catch (error) {
    console.error('测试失败:', error);
    process.exit(1);
  }
}

testLLMIsolation();